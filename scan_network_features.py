import os
import glob
import networkx as nx
import numpy as np
import time

def gen_graph(filename: str) -> nx.Graph:
    """
    从文件加载图形数据并生成无向图
    
    Args:
        filename: 包含边列表的文件路径
        
    Returns:
        nx.Graph: 生成的无向图对象
        
    Raises:
        RuntimeError: 当图加载失败时
    """
    try:
        G = nx.Graph()  # 创建一个空的无向图
        edges_data = np.loadtxt(filename, skiprows=1, usecols=[0, 1])  # 读取边数据
        edges = [(int(u), int(v)) for u, v in edges_data]
        G.add_edges_from(edges)  # 将边添加到图中
        return G  # 返回生成的图
    except Exception as e:
        raise RuntimeError(f"加载图形错误: {e}")

# 定义要扫描的目录路径
directory = r"D:\VS\code\networks"
print(f"开始扫描目录: {directory}")

# 查找所有txt文件（只在指定目录下，不包括子目录）
txt_files = glob.glob(os.path.join(directory, "*.txt"))
print(f"找到 {len(txt_files)} 个txt文件")

# 统计结果
results = []
start_time = time.time()
processed = 0

for file_path in txt_files:
    file_name = os.path.basename(file_path)
    print(f"正在处理 ({processed+1}/{len(txt_files)}): {file_name}")
    
    try:
        # 使用自定义函数加载图
        file_start = time.time()
        G = gen_graph(file_path)
        load_time = time.time() - file_start
        print(f"  图加载完成，用时: {load_time:.2f}秒")
        
        # 如果图为空，记录基本信息并跳过
        if G.number_of_nodes() == 0:
            print(f"  图为空或格式错误，跳过")
            results.append(f"文件: {file_name}, 状态: 空图或格式错误")
            processed += 1
            continue
            
        # 计算网络特征
        node_count = G.number_of_nodes()
        edge_count = G.number_of_edges()
        print(f"  节点数: {node_count}, 边数: {edge_count}")
        
        # 平均度
        calc_start = time.time()
        avg_degree = sum(dict(G.degree()).values()) / node_count
        print(f"  平均度: {avg_degree:.4f}, 计算用时: {time.time() - calc_start:.2f}秒")
        
        # 聚类系数
        calc_start = time.time()
        clustering_coef = nx.average_clustering(G)
        print(f"  聚类系数: {clustering_coef:.4f}, 计算用时: {time.time() - calc_start:.2f}秒")
        
        # 尝试计算其他特征（如果图连通）
        connected = "否"
        avg_path_length = "N/A"
        diameter = "N/A"
        
        try:
            # 平均路径长度
            calc_start = time.time()
            avg_path_length = nx.average_shortest_path_length(G)
            path_time = time.time() - calc_start
            
            # 直径
            calc_start = time.time()
            diameter = nx.diameter(G)
            diameter_time = time.time() - calc_start
            
            connected = "是"
            print(f"  平均路径长度: {avg_path_length:.4f}, 计算用时: {path_time:.2f}秒")
            print(f"  直径: {diameter}, 计算用时: {diameter_time:.2f}秒")
        except nx.NetworkXError:
            # 图不连通
            print(f"  图不连通，无法计算平均路径长度和直径")
        
        # 度分布
        calc_start = time.time()
        degrees = [d for n, d in G.degree()]
        max_degree = max(degrees) if degrees else 0
        min_degree = min(degrees) if degrees else 0
        print(f"  最大度: {max_degree}, 最小度: {min_degree}, 计算用时: {time.time() - calc_start:.2f}秒")
        
        # 记录结果
        results.append(f"文件: {file_name}\n"
                      f"  节点数: {node_count}\n"
                      f"  边数: {edge_count}\n"
                      f"  连通: {connected}\n"
                      f"  平均度: {avg_degree:.4f}\n"
                      f"  最大度: {max_degree}\n"
                      f"  最小度: {min_degree}\n"
                      f"  聚类系数: {clustering_coef:.4f}\n"
                      f"  平均路径长度: {avg_path_length}\n"
                      f"  直径: {diameter}\n")
        
        file_time = time.time() - file_start
        print(f"  文件处理完成，总用时: {file_time:.2f}秒")
    
    except Exception as e:
        # 处理读取或分析错误
        print(f"  处理错误: {str(e)}")
        results.append(f"文件: {file_name}, 错误: {str(e)}")
    
    processed += 1
    elapsed = time.time() - start_time
    avg_time = elapsed / processed
    est_remaining = avg_time * (len(txt_files) - processed)
    print(f"进度: {processed}/{len(txt_files)}, 已用时: {elapsed:.2f}秒, 预计剩余: {est_remaining:.2f}秒")
    print("-" * 50)

# 将结果写入输出文件
output_file = os.path.join(directory, "网络特征.txt")
with open(output_file, 'w', encoding='utf-8') as f:
    f.write(f"共找到 {len(txt_files)} 个txt文件\n\n")
    f.write("\n\n".join(results))

total_time = time.time() - start_time
print(f"统计完成，总用时: {total_time:.2f}秒")
print(f"结果已保存到 {output_file}")



