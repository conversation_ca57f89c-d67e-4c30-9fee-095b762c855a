import os
import glob
import networkx as nx
import numpy as np
import pandas as pd
import time
from datetime import datetime

def gen_graph(filename: str) -> nx.Graph:
    """
    从文件加载图形数据并生成无向图

    Args:
        filename: 包含边列表的文件路径

    Returns:
        nx.Graph: 生成的无向图对象

    Raises:
        RuntimeError: 当图加载失败时
    """
    try:
        G = nx.Graph()  # 创建一个空的无向图
        edges_data = np.loadtxt(filename, skiprows=1, usecols=[0, 1])  # 读取边数据
        edges = [(int(u), int(v)) for u, v in edges_data]
        G.add_edges_from(edges)  # 将边添加到图中
        return G  # 返回生成的图
    except Exception as e:
        raise RuntimeError(f"加载图形错误: {e}")

# 定义要扫描的目录路径
directory = r"D:\VS\code\networks"
current_dir = os.getcwd()  # 当前目录
print(f"开始扫描目录: {directory}")
print(f"当前工作目录: {current_dir}")

# 查找所有txt文件（只在指定目录下，不包括子目录）
txt_files = glob.glob(os.path.join(directory, "*.txt"))
print(f"找到 {len(txt_files)} 个txt文件")

# 统计结果 - 使用列表存储字典，便于转换为DataFrame
results = []
start_time = time.time()
processed = 0

for file_path in txt_files:
    file_name = os.path.basename(file_path)
    print(f"正在处理 ({processed+1}/{len(txt_files)}): {file_name}")

    try:
        # 使用自定义函数加载图
        file_start = time.time()
        G = gen_graph(file_path)
        load_time = time.time() - file_start
        print(f"  图加载完成，用时: {load_time:.2f}秒")

        # 如果图为空，记录基本信息并跳过
        if G.number_of_nodes() == 0:
            print(f"  图为空或格式错误，跳过")
            results.append({
                '文件名': file_name,
                '状态': '空图或格式错误',
                '节点数': 0,
                '边数': 0,
                '连通性': 'N/A',
                '平均度': 'N/A',
                '最大度': 'N/A',
                '最小度': 'N/A',
                '聚类系数': 'N/A',
                '平均路径长度': 'N/A',
                '直径': 'N/A',
                '连通分量数': 'N/A',
                '最大连通分量大小': 'N/A',
                '处理时间(秒)': 0
            })
            processed += 1
            continue

        # 计算网络特征
        node_count = G.number_of_nodes()
        edge_count = G.number_of_edges()
        print(f"  节点数: {node_count}, 边数: {edge_count}")

        # 平均度
        calc_start = time.time()
        avg_degree = sum(dict(G.degree()).values()) / node_count
        print(f"  平均度: {avg_degree:.4f}, 计算用时: {time.time() - calc_start:.2f}秒")

        # 聚类系数
        calc_start = time.time()
        clustering_coef = nx.average_clustering(G)
        print(f"  聚类系数: {clustering_coef:.4f}, 计算用时: {time.time() - calc_start:.2f}秒")

        # 连通性分析
        is_connected = nx.is_connected(G)
        connected = "是" if is_connected else "否"

        # 连通分量分析
        calc_start = time.time()
        connected_components = list(nx.connected_components(G))
        num_components = len(connected_components)
        largest_component_size = len(max(connected_components, key=len)) if connected_components else 0
        print(f"  连通分量数: {num_components}, 最大连通分量大小: {largest_component_size}, 计算用时: {time.time() - calc_start:.2f}秒")

        # 尝试计算其他特征（如果图连通）
        avg_path_length = "N/A"
        diameter = "N/A"

        if is_connected:
            try:
                # 平均路径长度
                calc_start = time.time()
                avg_path_length = nx.average_shortest_path_length(G)
                path_time = time.time() - calc_start

                # 直径
                calc_start = time.time()
                diameter = nx.diameter(G)
                diameter_time = time.time() - calc_start

                print(f"  平均路径长度: {avg_path_length:.4f}, 计算用时: {path_time:.2f}秒")
                print(f"  直径: {diameter}, 计算用时: {diameter_time:.2f}秒")
            except nx.NetworkXError as e:
                print(f"  计算路径特征时出错: {e}")
        else:
            print(f"  图不连通，无法计算全局平均路径长度和直径")

        # 度分布
        calc_start = time.time()
        degrees = [d for _, d in G.degree()]  # 修复未使用变量警告
        max_degree = max(degrees) if degrees else 0
        min_degree = min(degrees) if degrees else 0
        print(f"  最大度: {max_degree}, 最小度: {min_degree}, 计算用时: {time.time() - calc_start:.2f}秒")

        file_time = time.time() - file_start

        # 记录结果到字典
        result_dict = {
            '文件名': file_name,
            '状态': '成功',
            '节点数': node_count,
            '边数': edge_count,
            '连通性': connected,
            '平均度': round(avg_degree, 4),
            '最大度': max_degree,
            '最小度': min_degree,
            '聚类系数': round(clustering_coef, 4),
            '平均路径长度': round(avg_path_length, 4) if avg_path_length != "N/A" else "N/A",
            '直径': diameter,
            '连通分量数': num_components,
            '最大连通分量大小': largest_component_size,
            '处理时间(秒)': round(file_time, 2)
        }
        results.append(result_dict)

        print(f"  文件处理完成，总用时: {file_time:.2f}秒")

    except Exception as e:
        # 处理读取或分析错误
        print(f"  处理错误: {str(e)}")
        error_dict = {
            '文件名': file_name,
            '状态': f'错误: {str(e)}',
            '节点数': 'N/A',
            '边数': 'N/A',
            '连通性': 'N/A',
            '平均度': 'N/A',
            '最大度': 'N/A',
            '最小度': 'N/A',
            '聚类系数': 'N/A',
            '平均路径长度': 'N/A',
            '直径': 'N/A',
            '连通分量数': 'N/A',
            '最大连通分量大小': 'N/A',
            '处理时间(秒)': 0
        }
        results.append(error_dict)

    processed += 1
    elapsed = time.time() - start_time
    avg_time = elapsed / processed
    est_remaining = avg_time * (len(txt_files) - processed)
    print(f"进度: {processed}/{len(txt_files)}, 已用时: {elapsed:.2f}秒, 预计剩余: {est_remaining:.2f}秒")
    print("-" * 50)

# 创建DataFrame并保存为Excel
df = pd.DataFrame(results)

# 保存到当前目录
excel_file = os.path.join(current_dir, "网络特征统计.xlsx")
txt_file = os.path.join(current_dir, "网络特征统计.txt")

# 保存Excel文件
try:
    df.to_excel(excel_file, index=False, engine='openpyxl')
    print(f"Excel文件已保存到: {excel_file}")
except ImportError:
    print("警告: 未安装openpyxl，尝试使用xlsxwriter...")
    try:
        df.to_excel(excel_file, index=False, engine='xlsxwriter')
        print(f"Excel文件已保存到: {excel_file}")
    except ImportError:
        print("错误: 未安装Excel支持库，仅保存文本文件")

# 同时保存文本文件作为备份
with open(txt_file, 'w', encoding='utf-8') as f:
    f.write(f"网络特征统计报告\n")
    f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
    f.write(f"共找到 {len(txt_files)} 个txt文件\n")
    f.write(f"成功处理: {len([r for r in results if r['状态'] == '成功'])} 个文件\n\n")
    f.write("=" * 80 + "\n\n")

    for result in results:
        f.write(f"文件名: {result['文件名']}\n")
        for key, value in result.items():
            if key != '文件名':
                f.write(f"  {key}: {value}\n")
        f.write("\n" + "-" * 50 + "\n\n")

total_time = time.time() - start_time
print(f"统计完成，总用时: {total_time:.2f}秒")
print(f"文本文件已保存到: {txt_file}")

# 显示统计摘要
successful = len([r for r in results if r['状态'] == '成功'])
errors = len([r for r in results if r['状态'] != '成功' and '错误' in r['状态']])
empty = len([r for r in results if r['状态'] == '空图或格式错误'])

print(f"\n统计摘要:")
print(f"  总文件数: {len(txt_files)}")
print(f"  成功处理: {successful}")
print(f"  空图/格式错误: {empty}")
print(f"  处理错误: {errors}")

if successful > 0:
    # 显示一些基本统计
    successful_results = [r for r in results if r['状态'] == '成功']
    connected_count = len([r for r in successful_results if r['连通性'] == '是'])
    print(f"  连通图数量: {connected_count}/{successful}")

    if successful_results:
        avg_nodes = sum(r['节点数'] for r in successful_results) / len(successful_results)
        avg_edges = sum(r['边数'] for r in successful_results) / len(successful_results)
        print(f"  平均节点数: {avg_nodes:.1f}")
        print(f"  平均边数: {avg_edges:.1f}")



